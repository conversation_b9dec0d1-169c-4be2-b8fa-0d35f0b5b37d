public class TestH2OFair {
    public static void main(String[] args) {
        H2OFair h2o = new H2OFair();
        
        // Create hydrogen threads
        for (int i = 0; i < 6; i++) {
            new Thread(() -> {
                try {
                    h2o.hydrogen();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }, "H-" + i).start();
        }
        
        // Create oxygen threads
        for (int i = 0; i < 3; i++) {
            new Thread(() -> {
                try {
                    h2o.oxygen();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }, "O-" + i).start();
        }
        
        // Let it run for a bit
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("Total molecules formed: " + h2o.getH2OCount());
        System.exit(0);
    }
}
